# Zaroori libraries import karein
import getpass # Password ko surakshit roop se input lene ke liye
import sys     # System se judi commands ke liye

# --- Script Shuru ---
# Poore script ko try-except block me daalein taaki koi bhi error pakda ja sake
try:
    # Instaloader ko yahan import karein, taaki 'ImportError' bhi pakda ja sake
    import instaloader

    # 1. Instaloader ka ek object banayein
    L = instaloader.Instaloader()

    # 2. User se unka Instagram username aur password maange
    try:
        username = input("Apna Instagram username daalein: ")
        print(f"Debug: Username received: {username}")
        password = getpass.getpass("Apna Instagram password daalein (yeh dikhega nahi): ")
        print("Debug: Password received")

        # Instagram me login karein
        print("Instagram me login kiya ja raha hai...")
        L.login(username, password)
        print("<PERSON><PERSON> safal (successful)!")

    except Exception as e:
        print(f"\nLogin fail ho gaya: {e}")
        print("Kripya apna username aur password check karein, ya ho sakta hai 2-Factor Authentication on ho.")
        # Program ko pause karein taaki user error padh sake
        input("\nProgram band karne ke liye Enter dabayein...")
        sys.exit() # Agar login fail ho, to script band kar dein

    # 3. User se Instagram post ka URL maange
    post_url = input("\nJis Instagram video/post ka data nikalna hai, uska link daalein: ")

    # URL se post ka shortcode nikalein (jaise Cq-aBcDeFgH)
    try:
        # URL ke aakhri hisse se shortcode nikalne ki koshish
        if "/p/" in post_url:
            shortcode = post_url.split("/p/")[1].split("/")[0]
        elif "/reel/" in post_url:
            shortcode = post_url.split("/reel/")[1].split("/")[0]
        else:
            # Agar URL format alag hai to purana tareeka istemal karein
            shortcode = post_url.split("/")[-2]

        post = instaloader.Post.from_shortcode(L.context, shortcode)
    except Exception as e:
        print(f"\nURL me galti hai ya post nahi mil raha: {e}")
        input("\nProgram band karne ke liye Enter dabayein...")
        sys.exit()

    print(f"\n'{post.owner_username}' ke post ka data nikala ja raha hai...")

    # --- Data Nikalna ---

    # 4. Likes ki jaankari nikalna
    try:
        print("\nLikes ki list nikali ja rahi hai...")
        with open('likes.txt', 'w', encoding='utf-8') as f:
            f.write(f"Post URL: {post_url}\n")
            f.write(f"Post Malik (Owner): {post.owner_username}\n")
            f.write("--- Likes ki Suchi ---\n\n")

            count = 0
            for like in post.get_likes():
                f.write(f"Username: {like.username}, Profile Link: https://www.instagram.com/{like.username}/\n")
                count += 1
                if count % 50 == 0:
                    print(f"{count} likes ka data save ho gaya...")

        print(f"Kaam poora! Kul {count} likes ki jaankari 'likes.txt' file me save kar di gayi hai.")

    except Exception as e:
        print(f"Likes nikalne me samasya aayi: {e}")

    # 5. Comments ki jaankari nikalna
    try:
        print("\nComments ki list nikali ja rahi hai...")
        with open('comments.txt', 'w', encoding='utf-8') as f:
            f.write(f"Post URL: {post_url}\n")
            f.write(f"Post Malik (Owner): {post.owner_username}\n")
            f.write("--- Comments ki Suchi ---\n\n")

            count = 0
            for comment in post.get_comments():
                f.write(f"Username: {comment.owner.username}\n")
                f.write(f"Profile Link: https://www.instagram.com/{comment.owner.username}/\n")
                f.write(f"Comment: {comment.text}\n")
                f.write("---\n")
                count += 1
                if count % 20 == 0:
                    print(f"{count} comments ka data save ho gaya...")

        print(f"Kaam poora! Kul {count} comments ki jaankari 'comments.txt' file me save kar di gayi hai.")

    except Exception as e:
        print(f"Comments nikalne me samasya aayi: {e}")

    # 6. Views ki jaankari
    if post.is_video:
        print(f"\nIs video par kul views hain: {post.video_view_count}")
        with open('views.txt', 'w', encoding='utf-8') as f:
            f.write(f"Post URL: {post_url}\n")
            f.write(f"Post Malik (Owner): {post.owner_username}\n")
            f.write(f"Kul Views: {post.video_view_count}\n")
        print("View count 'views.txt' file me save kar diya gaya hai.")

    print("\nScript ne apna kaam poora kar liya hai.")

# Agar 'instaloader' library install nahi hai to yeh error aayega
except ImportError:
    print("\nERROR: 'instaloader' library nahi mili.")
    print("Yeh zaroori hai. Kripya Command Prompt (CMD) khol kar yeh command chalaayein:")
    print("\npip install instaloader\n")

# Kisi bhi aur anjaan error ke liye
except Exception as e:
    print(f"\nEk anjaan error aayi hai: {e}")
    print("Script band ho rahi hai.")

# Program ko ant me pause karein taaki user message padh sake
finally:
    input("\nProgram band karne ke liye Enter dabayein...")
